cmake 3.30.2
features core
portfile.cmake 3179577246ad40d0bc2187a916a21ca0451431c0f745ffbd4c7a5ad77ddcfb58
ports.cmake a9f8f745f9f4d225039f9810e79670f10f38c7455222865616ce430a06f75d1f
post_build_checks 2
powershell 7.2.24
triplet x64-windows
triplet_abi 4556164a2cd3dd6f4742101eabb46def7e71b6e5856faa88e5d005aac12a803c-1d3d767ab10b488977977b83f6c2da7e06afd80b1e375a2a41c60c343e7b3f94-3361a17d8afbd07b0f5c7f4dac4bd892b23a29d3
vcpkg-cmake 9c7659559d9cc11491c11db1ce7e20f3c25238a302d1288c8f57125eed7f4be6
vcpkg-cmake-config bf80e9e0735ebdc85f899715018b274643a44e1096a31efdf4ed1b7b89209b2e
vcpkg.json c6172fba1d57280b910b63f1cee01a9fd54745e4c494705b63f78324cd52db0f
vcpkg_copy_pdbs d15c2f2822d93ecb9023af44803328e491c5374136e0813600289582a72d196d
vcpkg_fixup_pkgconfig 1a15f6c6d8e2b244d83a7514a0412d339127d2217d1df60ad1388b546c85f777
vcpkg_from_git 96ed81968f76354c00096dd8cd4e63c6a235fa969334a11ab18d11c0c512ff58
vcpkg_from_github 1284881728e98a182fc63e841be04e39b8c94753fdc361603c72a63c1adcf846
vcpkg_install_copyright ba6c169ab4e59fa05682e530cdeb883767de22c8391f023d4e6844a7ec5dd3d2
