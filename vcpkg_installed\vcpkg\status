Package: vcpkg-cmake-config
Version: 2024-05-23
Architecture: x64-windows
Multi-Arch: same
Abi: bf80e9e0735ebdc85f899715018b274643a44e1096a31efdf4ed1b7b89209b2e
Status: install ok installed

Package: vcpkg-cmake
Version: 2024-04-23
Architecture: x64-windows
Multi-Arch: same
Abi: 9c7659559d9cc11491c11db1ce7e20f3c25238a302d1288c8f57125eed7f4be6
Status: install ok installed

Package: libwebp
Version: 1.5.0
Port-Version: 1
Depends: libwebp, vcpkg-cmake, vcpkg-cmake-config
Architecture: x64-windows
Multi-Arch: same
Abi: 72c6785ebb2850d8b6b00e7850cfe5dd0b699bdd428e98bb7116b5e9def35305
Description: WebP codec: library to encode and decode images in WebP format
Default-Features: libwebpmux, nearlossless, simd
Status: install ok installed

Package: libwebp
Feature: libwebpmux
Architecture: x64-windows
Multi-Arch: same
Description: Build the libwebpmux library
Status: install ok installed

Package: libwebp
Feature: nearlossless
Architecture: x64-windows
Multi-Arch: same
Description: Enable near-lossless encoding
Status: install ok installed

Package: libwebp
Feature: simd
Architecture: x64-windows
Multi-Arch: same
Description: Enable any SIMD optimization.
Status: install ok installed

Package: libwebp
Feature: unicode
Architecture: x64-windows
Multi-Arch: same
Description: Build Unicode executables. (Adds definition UNICODE and _UNICODE)
Status: install ok installed

Package: zlib
Version: 1.3.1
Depends: vcpkg-cmake
Architecture: x64-windows
Multi-Arch: same
Abi: 323e4c83967d27e89f7fc4717831984872b70ae40c92da2215c57bb049c347fb
Description: A compression library
Status: install ok installed

Package: liblzma
Version: 5.8.1
Depends: vcpkg-cmake, vcpkg-cmake-config
Architecture: x64-windows
Multi-Arch: same
Abi: 1423858e0d8735d9b990a7e552f7747e80b56a91b3a68d6d80d7fdbeefffd7fe
Description: Compression library with an API similar to that of zlib.
Status: install ok installed

Package: libjpeg-turbo
Version: 3.1.0
Port-Version: 1
Depends: vcpkg-cmake, vcpkg-cmake-config
Architecture: x64-windows
Multi-Arch: same
Abi: d93f385cb6eb7c04dfe737d4285f0bff2be4f62440b195f22f86cb4839d7f6bf
Description: libjpeg-turbo is a JPEG image codec that uses SIMD instructions (MMX, SSE2, NEON, AltiVec) to accelerate baseline JPEG compression and decompression on x86, x86-64, ARM, and PowerPC systems.
Status: install ok installed

Package: tiff
Version: 4.7.0
Depends: vcpkg-cmake, vcpkg-cmake-config
Architecture: x64-windows
Multi-Arch: same
Abi: ec5034ecd75e0f46a35564a7f3656af1f542fa3091d9745027997133ae1f88f8
Description: A library that supports the manipulation of TIFF image files
Default-Features: jpeg, lzma, zip
Status: install ok installed

Package: tiff
Feature: jpeg
Depends: libjpeg-turbo
Architecture: x64-windows
Multi-Arch: same
Description: Support JPEG compression in TIFF image files
Status: install ok installed

Package: tiff
Feature: lzma
Depends: liblzma
Architecture: x64-windows
Multi-Arch: same
Description: Support LZMA compression in TIFF image files
Status: install ok installed

Package: tiff
Feature: zip
Depends: zlib
Architecture: x64-windows
Multi-Arch: same
Description: Support ZIP/deflate compression in TIFF image files
Status: install ok installed

Package: quirc
Version: 1.2
Depends: vcpkg-cmake, vcpkg-cmake-config
Architecture: x64-windows
Multi-Arch: same
Abi: 101eae54f3c55d70c3912c27e2f759cb0eca9c611e71b0c9d8ed354031fe7c8b
Description: quirc is one of the C library available for scanning QR Codes
Status: install ok installed

Package: libpng
Version: 1.6.47
Depends: vcpkg-cmake, vcpkg-cmake-config, zlib
Architecture: x64-windows
Multi-Arch: same
Abi: f51170e8a629477ee322f5abb669f2dd09357fbc38d5d4f72253060a6573ea31
Description: libpng is a library implementing an interface for reading and writing PNG (Portable Network Graphics) format files
Status: install ok installed

Package: abseil
Version: 20250127.1
Port-Version: 1
Depends: vcpkg-cmake, vcpkg-cmake-config
Architecture: x64-windows
Multi-Arch: same
Abi: d96dd3e2dbd91cb53c626233a8a83ceff4aa0b8a10605a70e2593918c278c0c8
Description: Abseil is an open-source collection of C++ library code designed to augment the C++ standard library. The Abseil library code is collected from Google's own C++ code base, has been extensively tested and used in production, and is the same code we depend on in our daily coding lives.
    In some cases, Abseil provides pieces missing from the C++ standard; in others, Abseil provides alternatives to the standard for special needs we've found through usage in the Google code base. We denote those cases clearly within the library code we provide you.
    Abseil is not meant to be a competitor to the standard library; we've just found that many of these utilities serve a purpose within our code base, and we now want to provide those resources to the C++ community as a whole.
Status: install ok installed

Package: utf8-range
Version: 5.29.3
Depends: abseil, vcpkg-cmake, vcpkg-cmake-config
Architecture: x64-windows
Multi-Arch: same
Abi: 9c53526aaae464372c463f7517a2b7d8c2fcebca62c375333e6b3b76cfc41f25
Description: Fast UTF-8 validation with Range algorithm (NEON+SSE4+AVX2)
Status: install ok installed

Package: protobuf
Version: 5.29.3
Depends: abseil, protobuf, utf8-range, vcpkg-cmake, vcpkg-cmake-config
Architecture: x64-windows
Multi-Arch: same
Abi: af6f4c541d296a37fcbc72ea2acbaa57957ef5be618d108680e308c6560eac4e
Description: Google's language-neutral, platform-neutral, extensible mechanism for serializing structured data.
Status: install ok installed

Package: vcpkg-get-python-packages
Version: 2025-04-05
Architecture: x64-windows
Multi-Arch: same
Abi: ad1e3d4fb9f7e13b78c23f9a7b8ea1186bf5e970eee3b1bc1d8ee26bb453ca84
Status: install ok installed

Package: opencv3
Version: 3.4.20
Depends: vcpkg-cmake, vcpkg-cmake-config, vcpkg-get-python-packages, zlib
Architecture: x64-windows
Multi-Arch: same
Abi: 733632d0015cc96529142830776e1af632bf6bd79b3e0acee0ae337d5a6530e2
Description: Open Source Computer Vision Library
Default-Features: dnn, gapi, jpeg, msmf, png, quirc, tiff, webp
Status: install ok installed

Package: opencv3
Feature: dnn
Depends: opencv3
Architecture: x64-windows
Multi-Arch: same
Description: Enable dnn module
Status: install ok installed

Package: opencv3
Feature: flann
Depends: protobuf
Architecture: x64-windows
Multi-Arch: same
Description: opencv_flann module
Status: install ok installed

Package: opencv3
Feature: gapi
Architecture: x64-windows
Multi-Arch: same
Description: Enable gapi module
Status: install ok installed

Package: opencv3
Feature: jpeg
Depends: libjpeg-turbo
Architecture: x64-windows
Multi-Arch: same
Description: JPEG support for opencv
Status: install ok installed

Package: opencv3
Feature: msmf
Architecture: x64-windows
Multi-Arch: same
Description: Microsoft Media Foundation support for opencv
Status: install ok installed

Package: opencv3
Feature: png
Depends: libpng
Architecture: x64-windows
Multi-Arch: same
Description: PNG support for opencv
Status: install ok installed

Package: opencv3
Feature: quirc
Depends: quirc
Architecture: x64-windows
Multi-Arch: same
Description: Enable QR code module
Status: install ok installed

Package: opencv3
Feature: tiff
Depends: tiff
Architecture: x64-windows
Multi-Arch: same
Description: TIFF support for opencv
Status: install ok installed

Package: opencv3
Feature: webp
Depends: libwebp
Architecture: x64-windows
Multi-Arch: same
Description: WebP support for opencv
Status: install ok installed

Package: vcpkg-cmake-get-vars
Version: 2024-09-22
Depends: vcpkg-cmake
Architecture: x64-windows
Multi-Arch: same
Abi: 0227cb6b897a4c6443dcba634398daf3f468b99af42d6f7408686ab30d74bc60
Status: install ok installed

Package: openssl
Version: 3.5.0
Port-Version: 1
Depends: vcpkg-cmake, vcpkg-cmake-config, vcpkg-cmake-get-vars
Architecture: x64-windows
Multi-Arch: same
Abi: e498e51754dfefc7ccc77c819c2fd765b42bcd03599f559e86cc009ded226400
Description: OpenSSL is an open source project that provides a robust, commercial-grade, and full-featured toolkit for the Transport Layer Security (TLS) and Secure Sockets Layer (SSL) protocols. It is also a general-purpose cryptography library.
Status: install ok installed

Package: paho-mqtt
Version: 1.3.14
Depends: openssl, vcpkg-cmake, vcpkg-cmake-config
Architecture: x64-windows
Multi-Arch: same
Abi: 3c7c53380dcc5bfce2c8b3901ca447eb341b04173c1b8dfb39f03dd3c39a64c2
Description: Paho project provides open-source client implementations of MQTT and MQTT-SN messaging protocols aimed at new, existing, and emerging applications for the Internet of Things
Status: install ok installed

Package: paho-mqttpp3
Version: 1.5.2
Depends: paho-mqtt, vcpkg-cmake, vcpkg-cmake-config
Architecture: x64-windows
Multi-Arch: same
Abi: 9025b4d3fe3ab730106e418e981c0474a2a45b75d64a311ad66999bdc9fbb2e1
Description: Paho project provides open-source C++ wrapper for Paho C library
Default-Features: ssl
Status: install ok installed

Package: paho-mqttpp3
Feature: ssl
Depends: openssl
Architecture: x64-windows
Multi-Arch: same
Description: Build with SSL support
Status: install ok installed

Package: vcpkg-boost
Version: 2025-03-29
Depends: vcpkg-cmake, vcpkg-cmake-config
Architecture: x64-windows
Multi-Arch: same
Abi: 811b6cbec8fdd466406370c814ba566b7eb4c22c288fc8bca05e1a1696630acb
Status: install ok installed

Package: boost-uninstall
Version: 1.88.0
Architecture: x64-windows
Multi-Arch: same
Abi: 4001974efbd20f057d2afcd8099431f70880e1900838cdc9bfdf2cc74a05026c
Description: Internal vcpkg port used to uninstall Boost
Status: install ok installed

Package: boost-cmake
Version: 1.88.0
Depends: boost-uninstall, vcpkg-boost
Architecture: x64-windows
Multi-Arch: same
Abi: 052a84d0911955f0df97c3ef3c88351837c96bdf00bc3be8942b9d2c98c05fb3
Description: Boost cmake module
Status: install ok installed

Package: boost-headers
Version: 1.88.0
Depends: boost-cmake
Architecture: x64-windows
Multi-Arch: same
Abi: fc10c92ed2844dacbf21970b0c7bf37f00aad4a50528c0494f6a4d94b07270a2
Description: Boost headers module
Status: install ok installed

Package: boost-config
Version: 1.88.0
Depends: boost-cmake, boost-headers
Architecture: x64-windows
Multi-Arch: same
Abi: 4073a56b7a3bcaa4ac2ad08d471fa6b2fe9a5ae908e53732d4582ef0363625f9
Description: Boost config module
Status: install ok installed

Package: boost-assert
Version: 1.88.0
Depends: boost-cmake, boost-config, boost-headers
Architecture: x64-windows
Multi-Arch: same
Abi: 07465b32f6b912f563c4c11c7ad9fbdcff3a62c54cc419b39e970d9e989eb204
Description: Boost assert module
Status: install ok installed

Package: boost-throw-exception
Version: 1.88.0
Depends: boost-assert, boost-cmake, boost-config, boost-headers
Architecture: x64-windows
Multi-Arch: same
Abi: 93daad3210570b95a7bfa10146fac5a4392db8d86903c545ed3361c57ee653d3
Description: Boost throw_exception module
Status: install ok installed

Package: boost-static-assert
Version: 1.88.0
Depends: boost-cmake, boost-config, boost-headers
Architecture: x64-windows
Multi-Arch: same
Abi: 952a7a7033284d1bdf8d5a2274e9752f2fce5d2e6f93ef5fcbd3ca3d27af3657
Description: Boost static_assert module
Status: install ok installed

Package: boost-core
Version: 1.88.0
Depends: boost-assert, boost-cmake, boost-config, boost-headers, boost-static-assert, boost-throw-exception
Architecture: x64-windows
Multi-Arch: same
Abi: aef7ea2d58ff5c47cf0a9f71a34def19bae535c8d2867991d9aef48660c48687
Description: Boost core module
Status: install ok installed

Package: boost-mp11
Version: 1.88.0
Depends: boost-cmake, boost-config, boost-headers
Architecture: x64-windows
Multi-Arch: same
Abi: 49e6866ec07ad2c272d17107fc9d11379b87049d218f6cae026c46f16ecc0090
Description: Boost mp11 module
Status: install ok installed

Package: boost-describe
Version: 1.88.0
Depends: boost-cmake, boost-config, boost-headers, boost-mp11
Architecture: x64-windows
Multi-Arch: same
Abi: 14babaabcbd56be3f1e057259b0a03418572675190bad28067197b5b2e3edc88
Description: Boost describe module
Status: install ok installed

Package: boost-container-hash
Version: 1.88.0
Depends: boost-cmake, boost-config, boost-describe, boost-headers, boost-mp11
Architecture: x64-windows
Multi-Arch: same
Abi: 6c302567d4baec2352bb229f1be4a597d6f7ea584845342f8d2654e683171176
Description: Boost container_hash module
Status: install ok installed

Package: boost-type-index
Version: 1.88.0
Depends: boost-cmake, boost-config, boost-container-hash, boost-core, boost-headers, boost-throw-exception
Architecture: x64-windows
Multi-Arch: same
Abi: 353caea9e4dc4021a5ce96f1f211ce0ecb9d123ac62cd961ac62c154d1fe0f98
Description: Boost type_index module
Status: purge ok not-installed

Package: boost-preprocessor
Version: 1.88.0
Depends: boost-cmake, boost-config, boost-headers
Architecture: x64-windows
Multi-Arch: same
Abi: 16f780025e79a1682f4ec4a43c68e61120921a9f1ec2c1cee713182206a462bb
Description: Boost preprocessor module
Status: install ok installed

Package: boost-type-traits
Version: 1.88.0
Depends: boost-cmake, boost-config, boost-headers, boost-static-assert
Architecture: x64-windows
Multi-Arch: same
Abi: a5aa142804063e63771c44f52554245be3221b5b6f9ccb8caea465a09470b921
Description: Boost type_traits module
Status: install ok installed

Package: boost-tuple
Version: 1.88.0
Depends: boost-cmake, boost-config, boost-core, boost-headers, boost-static-assert, boost-type-traits
Architecture: x64-windows
Multi-Arch: same
Abi: 370faf23af7a37e2c2c74ddc491d5fb98fbb27360b794afab5e673fe174df546
Description: Boost tuple module
Status: install ok installed

Package: boost-io
Version: 1.88.0
Depends: boost-cmake, boost-config, boost-headers
Architecture: x64-windows
Multi-Arch: same
Abi: 8f6501d7bd59012ea20bc455d77dbcb49b82168217224c122363cc11cf49e2fa
Description: Boost io module
Status: install ok installed

Package: boost-utility
Version: 1.88.0
Depends: boost-assert, boost-cmake, boost-config, boost-core, boost-headers, boost-io, boost-preprocessor, boost-throw-exception, boost-type-traits
Architecture: x64-windows
Multi-Arch: same
Abi: df6321f81416718447e9b6b5505358923dc37b46f8bc6b86347e19c72a510387
Description: Boost utility module
Status: install ok installed

Package: boost-predef
Version: 1.88.0
Depends: boost-cmake, boost-config, boost-headers
Architecture: x64-windows
Multi-Arch: same
Abi: a559f8b0535d972afbcf185b73fd7c6a0dd5669f30cc8e4ab348c75ba3a1ac17
Description: Boost predef module
Status: install ok installed

Package: boost-mpl
Version: 1.88.0
Depends: boost-cmake, boost-config, boost-core, boost-headers, boost-predef, boost-preprocessor, boost-static-assert, boost-type-traits, boost-utility
Architecture: x64-windows
Multi-Arch: same
Abi: ad4d51236eb06a44f44a0ba8dabfcbacad3dc41c121dd9d160048a08e35c3c01
Description: Boost mpl module
Status: install ok installed

Package: boost-typeof
Version: 1.88.0
Depends: boost-cmake, boost-config, boost-headers
Architecture: x64-windows
Multi-Arch: same
Abi: 0152f96394e0966bb517f24bd63dada83abbd7020f4f0d3fa23343af5c9071f9
Description: Boost typeof module
Status: install ok installed

Package: boost-detail
Version: 1.88.0
Depends: boost-cmake, boost-config, boost-core, boost-headers, boost-preprocessor, boost-static-assert, boost-type-traits
Architecture: x64-windows
Multi-Arch: same
Abi: 6a7462f2613b0a72a303fc54651dfa38d94ce925b973e48da45a6ed3961fd285
Description: Boost detail module
Status: install ok installed

Package: boost-function-types
Version: 1.88.0
Depends: boost-cmake, boost-config, boost-core, boost-detail, boost-headers, boost-mpl, boost-preprocessor, boost-type-traits
Architecture: x64-windows
Multi-Arch: same
Abi: 0275834fac50c5d854f4d02e63a00a02616188b9f284b13e6c4476649c382d14
Description: Boost function_types module
Status: install ok installed

Package: boost-bind
Version: 1.88.0
Depends: boost-cmake, boost-config, boost-core, boost-headers
Architecture: x64-windows
Multi-Arch: same
Abi: c2357014d9bdc9332886ca0a3038879545b447a42ae5f2ec2d98b6437ca0008d
Description: Boost bind module
Status: install ok installed

Package: boost-function
Version: 1.88.0
Depends: boost-assert, boost-bind, boost-cmake, boost-config, boost-core, boost-headers, boost-throw-exception
Architecture: x64-windows
Multi-Arch: same
Abi: 11ffe4f9e84da05c0e36b6b311a38587aa4addb278adbd1319b118d5e6b009fc
Description: Boost function module
Status: install ok installed

Package: boost-functional
Version: 1.88.0
Depends: boost-cmake, boost-config, boost-core, boost-function, boost-function-types, boost-headers, boost-mpl, boost-preprocessor, boost-type-traits, boost-typeof, boost-utility
Architecture: x64-windows
Multi-Arch: same
Abi: c58a521e4745c6e34675c0e8e6b3d0a37735a986fa87ce73920bc36f486f08cd
Description: Boost functional module
Status: install ok installed

Package: boost-fusion
Version: 1.88.0
Depends: boost-cmake, boost-config, boost-container-hash, boost-core, boost-function-types, boost-functional, boost-headers, boost-mpl, boost-preprocessor, boost-static-assert, boost-tuple, boost-type-traits, boost-typeof, boost-utility
Architecture: x64-windows
Multi-Arch: same
Abi: 2195619325a27667d2b1fd78fb56fbcc79f11907b74f04d16b02fc1343aa6562
Description: Boost fusion module
Status: install ok installed

Package: boost-hana
Version: 1.88.0
Depends: boost-cmake, boost-config, boost-core, boost-fusion, boost-headers, boost-mpl, boost-tuple
Architecture: x64-windows
Multi-Arch: same
Abi: 0e94e7ad4e50326dfcd7cd2b44b14ce069fffd21068eab7b7e6bb9eb42480457
Description: Boost hana module
Status: purge ok not-installed

Package: boost-yap
Version: 1.88.0
Depends: boost-cmake, boost-config, boost-hana, boost-headers, boost-preprocessor, boost-type-index
Architecture: x64-windows
Multi-Arch: same
Abi: 9db6feeda754e6286b834fec30340cd7c2c9fa72b446196a4f69184c93054ccd
Description: Boost yap module
Status: purge ok not-installed

Package: boost-integer
Version: 1.88.0
Depends: boost-assert, boost-cmake, boost-config, boost-core, boost-headers, boost-static-assert, boost-throw-exception, boost-type-traits
Architecture: x64-windows
Multi-Arch: same
Abi: 27ab9c3a1f46e32b04c6e744ac01b2fa355e768302ba18ab527a830292af3a4d
Description: Boost integer module
Status: install ok installed

Package: boost-variant
Version: 1.88.0
Depends: boost-assert, boost-cmake, boost-config, boost-container-hash, boost-core, boost-detail, boost-headers, boost-integer, boost-mpl, boost-preprocessor, boost-static-assert, boost-throw-exception, boost-type-index, boost-type-traits, boost-utility
Architecture: x64-windows
Multi-Arch: same
Abi: 6ed41773494c16e34b5ce36cc37915357a46c0271dc1a5c710976dd23eb75163
Description: Boost variant module
Status: purge ok not-installed

Package: boost-unordered
Version: 1.88.0
Depends: boost-assert, boost-cmake, boost-config, boost-container-hash, boost-core, boost-headers, boost-mp11, boost-predef, boost-throw-exception
Architecture: x64-windows
Multi-Arch: same
Abi: 8f2623bf847341bade39abe8705b472e22666b512a35cbcf33a9dda039c5eb13
Description: Boost unordered module
Status: install ok installed

Package: boost-winapi
Version: 1.88.0
Depends: boost-cmake, boost-config, boost-headers, boost-predef
Architecture: x64-windows
Multi-Arch: same
Abi: 89c7bdcfbac84952d9a771e5682db1aacaa9f40f1223820bb581b30e72980233
Description: Boost winapi module
Status: install ok installed

Package: boost-variant2
Version: 1.88.0
Depends: boost-assert, boost-cmake, boost-config, boost-headers, boost-mp11
Architecture: x64-windows
Multi-Arch: same
Abi: 97436621deb4ba315881d3ee03b501642f9790a4946e100479eb3c8c4153b5aa
Description: Boost variant2 module
Status: install ok installed

Package: boost-system
Version: 1.88.0
Depends: boost-assert, boost-cmake, boost-config, boost-headers, boost-throw-exception, boost-variant2, boost-winapi
Architecture: x64-windows
Multi-Arch: same
Abi: 55f78d27d7e48ee2013b41e2d6f088bd335ce8f4261b30850caf7f498f6cb755
Description: Boost system module
Status: install ok installed

Package: boost-smart-ptr
Version: 1.88.0
Depends: boost-assert, boost-cmake, boost-config, boost-core, boost-headers, boost-throw-exception
Architecture: x64-windows
Multi-Arch: same
Abi: 674bd76712308001d207fa908ba6e7924583a67051bf55cbf870cc3fa8a6dbd1
Description: Boost smart_ptr module
Status: install ok installed

Package: boost-optional
Version: 1.88.0
Depends: boost-assert, boost-cmake, boost-config, boost-core, boost-headers, boost-throw-exception, boost-type-traits
Architecture: x64-windows
Multi-Arch: same
Abi: 14c6564e4c6aa346390b75c077549039854cc8203dd7c532149af04958dadfb4
Description: Boost optional module
Status: install ok installed

Package: boost-move
Version: 1.88.0
Depends: boost-cmake, boost-config, boost-headers
Architecture: x64-windows
Multi-Arch: same
Abi: 7abf24c87ab289fcae10bcf0f9460337e5d8daa4f7ebe3a9af710dd297f4c129
Description: Boost move module
Status: install ok installed

Package: boost-exception
Version: 1.88.0
Depends: boost-assert, boost-cmake, boost-config, boost-core, boost-headers, boost-smart-ptr, boost-throw-exception, boost-tuple, boost-type-traits
Architecture: x64-windows
Multi-Arch: same
Abi: d238ae159f122fee8410fa9581a773832dbf746916ddde143269f5065d9c5240
Description: Boost exception module
Status: install ok installed

Package: boost-concept-check
Version: 1.88.0
Depends: boost-cmake, boost-config, boost-headers, boost-preprocessor, boost-static-assert, boost-type-traits
Architecture: x64-windows
Multi-Arch: same
Abi: 7dfd0e95b1401c00de58d96f9fb4a71c5d34167f86d3f460046d5d31f8f04fc0
Description: Boost concept_check module
Status: install ok installed

Package: boost-iterator
Version: 1.88.0
Depends: boost-cmake, boost-concept-check, boost-config, boost-core, boost-detail, boost-fusion, boost-headers, boost-mp11, boost-mpl, boost-optional, boost-type-traits, boost-utility
Architecture: x64-windows
Multi-Arch: same
Abi: ee999f2dcbc5994a94f74148f04996dd43a6b65b0c32106386e83893221ccb99
Description: Boost iterator module
Status: install ok installed

Package: boost-tokenizer
Version: 1.88.0
Depends: boost-assert, boost-cmake, boost-config, boost-core, boost-headers, boost-iterator, boost-throw-exception, boost-type-traits
Architecture: x64-windows
Multi-Arch: same
Abi: a5d949758f14bbe6a8f4d52a102f2a1c6b04380a889e9230e824bbae08d1eafd
Description: Boost tokenizer module
Status: install ok installed

Package: boost-regex
Version: 1.88.0
Depends: boost-assert, boost-cmake, boost-concept-check, boost-config, boost-container-hash, boost-core, boost-headers, boost-integer, boost-mpl, boost-predef, boost-smart-ptr, boost-static-assert, boost-throw-exception, boost-type-traits
Architecture: x64-windows
Multi-Arch: same
Abi: 60938be7199c29f27efe30f796823995d928ad574bc7d2cac47e74f0fbbbcc6f
Description: Boost regex module
Status: install ok installed

Package: boost-conversion
Version: 1.88.0
Depends: boost-assert, boost-cmake, boost-config, boost-headers, boost-smart-ptr, boost-throw-exception
Architecture: x64-windows
Multi-Arch: same
Abi: 748ef8594d609aff80663194f543bf5d5cd325a11a3b9739b6e47910f86862b4
Description: Boost conversion module
Status: install ok installed

Package: boost-array
Version: 1.88.0
Depends: boost-assert, boost-cmake, boost-config, boost-headers, boost-static-assert, boost-throw-exception
Architecture: x64-windows
Multi-Arch: same
Abi: 5ce8ec2b4c8f56886c23bcfdfcf42949f22828b429bc45b8d523cf7a1dd937d8
Description: Boost array module
Status: install ok installed

Package: boost-range
Version: 1.88.0
Depends: boost-array, boost-assert, boost-cmake, boost-concept-check, boost-config, boost-container-hash, boost-conversion, boost-core, boost-detail, boost-headers, boost-iterator, boost-mpl, boost-optional, boost-preprocessor, boost-regex, boost-static-assert, boost-tuple, boost-type-traits, boost-utility
Architecture: x64-windows
Multi-Arch: same
Abi: e85d6ed0090f7f60eda8057c6ac074a9bd1e0835f795a917e566fb6f3e0f91ea
Description: Boost range module
Status: install ok installed

Package: boost-numeric-conversion
Version: 1.88.0
Depends: boost-cmake, boost-config, boost-conversion, boost-core, boost-headers, boost-mpl, boost-preprocessor, boost-throw-exception, boost-type-traits
Architecture: x64-windows
Multi-Arch: same
Abi: 8f05da7c36848fe0c0bf3da8af04bc399cf7a8c2a53322ef77fb1d81f5f5809e
Description: Boost numeric_conversion module
Status: install ok installed

Package: boost-intrusive
Version: 1.88.0
Depends: boost-assert, boost-cmake, boost-config, boost-headers, boost-move
Architecture: x64-windows
Multi-Arch: same
Abi: 92878effcd5b6a407c9cb8e7a73fa97f244256423fa8e547989750386aec563a
Description: Boost intrusive module
Status: install ok installed

Package: boost-container
Version: 1.88.0
Depends: boost-assert, boost-cmake, boost-config, boost-headers, boost-intrusive, boost-move
Architecture: x64-windows
Multi-Arch: same
Abi: e5c1f7f563ec7d71401a3dc8c3304fb410770cf0d07164851e06ee288b09713f
Description: Boost container module
Status: install ok installed

Package: boost-lexical-cast
Version: 1.88.0
Depends: boost-cmake, boost-config, boost-container, boost-core, boost-headers, boost-throw-exception, boost-type-traits
Architecture: x64-windows
Multi-Arch: same
Abi: eddac535e08fc1362d53afaeaac19600724aa2f8a76cbfe9f279061b965fc34d
Description: Boost lexical_cast module
Status: install ok installed

Package: boost-algorithm
Version: 1.88.0
Depends: boost-array, boost-assert, boost-bind, boost-cmake, boost-concept-check, boost-config, boost-core, boost-exception, boost-function, boost-headers, boost-iterator, boost-mpl, boost-range, boost-regex, boost-static-assert, boost-throw-exception, boost-tuple, boost-type-traits, boost-unordered
Architecture: x64-windows
Multi-Arch: same
Abi: fb915a12485fee587cef148a46902c6a3b6c8ed40f36e0ad6d0fc4cb8bcdabcd
Description: Boost algorithm module
Status: install ok installed

Package: boost-date-time
Version: 1.88.0
Depends: boost-algorithm, boost-assert, boost-cmake, boost-config, boost-core, boost-headers, boost-io, boost-lexical-cast, boost-numeric-conversion, boost-range, boost-smart-ptr, boost-static-assert, boost-throw-exception, boost-tokenizer, boost-type-traits, boost-utility, boost-winapi
Architecture: x64-windows
Multi-Arch: same
Abi: 7c7e1c46042213d50210fa9ce8b2b6558ab4c602b3db616e235fc7111785493f
Description: Boost date_time module
Status: install ok installed

Package: boost-ratio
Version: 1.88.0
Depends: boost-cmake, boost-config, boost-headers
Architecture: x64-windows
Multi-Arch: same
Abi: 501519afc767b513b8fa6c0a5698c225b61b8fb0939807fcaf1e57c3b7af75ff
Description: Boost ratio module
Status: install ok installed

Package: boost-chrono
Version: 1.88.0
Depends: boost-assert, boost-cmake, boost-config, boost-core, boost-headers, boost-integer, boost-move, boost-mpl, boost-predef, boost-ratio, boost-static-assert, boost-system, boost-throw-exception, boost-type-traits, boost-typeof, boost-utility, boost-winapi
Architecture: x64-windows
Multi-Arch: same
Abi: 580ed43d1ffd112333637d85b467a3c0a2ca017cab4328b7f06a7fb08a38d93f
Description: Boost chrono module
Status: install ok installed

Package: boost-align
Version: 1.88.0
Depends: boost-assert, boost-cmake, boost-config, boost-core, boost-headers, boost-static-assert
Architecture: x64-windows
Multi-Arch: same
Abi: bf2ab779d969def90289f0983e4297b597fbb5b2537ee1bdc92ae1ddd4bc4b62
Description: Boost align module
Status: install ok installed

Package: boost-atomic
Version: 1.88.0
Depends: boost-align, boost-assert, boost-cmake, boost-config, boost-headers, boost-predef, boost-preprocessor, boost-type-traits, boost-winapi
Architecture: x64-windows
Multi-Arch: same
Abi: 1e241c81d1172e02bbe35adcdcb06eeb44f98625624d550ef50c16737d8bb3e5
Description: Boost atomic module
Status: install ok installed

Package: boost-thread
Version: 1.88.0
Depends: boost-assert, boost-atomic, boost-bind, boost-chrono, boost-cmake, boost-concept-check, boost-config, boost-container, boost-container-hash, boost-core, boost-date-time, boost-exception, boost-function, boost-headers, boost-io, boost-move, boost-optional, boost-predef, boost-preprocessor, boost-smart-ptr, boost-static-assert, boost-system, boost-throw-exception, boost-tuple, boost-type-traits, boost-utility, boost-winapi
Architecture: x64-windows
Multi-Arch: same
Abi: 5c1fead8064291341b24d25b086c289bbf4071a6086632a3d9e56ae1f70f2a55
Description: Boost thread module
Status: install ok installed

Package: boost-proto
Version: 1.88.0
Depends: boost-cmake, boost-config, boost-core, boost-fusion, boost-headers, boost-mpl, boost-preprocessor, boost-range, boost-static-assert, boost-type-traits, boost-typeof, boost-utility
Architecture: x64-windows
Multi-Arch: same
Abi: 74a767573e1a14b2bbe87e106aef14550794d96b6c57a4c647b056fb746c8f15
Description: Boost proto module
Status: purge ok not-installed

Package: boost-pool
Version: 1.88.0
Depends: boost-assert, boost-cmake, boost-config, boost-headers, boost-integer, boost-throw-exception, boost-type-traits, boost-winapi
Architecture: x64-windows
Multi-Arch: same
Abi: fcde311f2dd8ca90c9c94ab496542453a27b88d03bbf08cfb8f9efeab9ee154f
Description: Boost pool module
Status: purge ok not-installed

Package: boost-phoenix
Version: 1.88.0
Depends: boost-assert, boost-bind, boost-cmake, boost-config, boost-core, boost-function, boost-fusion, boost-headers, boost-mpl, boost-predef, boost-preprocessor, boost-proto, boost-range, boost-smart-ptr, boost-type-traits, boost-utility
Architecture: x64-windows
Multi-Arch: same
Abi: 2b0eb8a24a94d3927372571a3b70b01deb4414ac4ffd7aac13936bed7df9aa69
Description: Boost phoenix module
Status: purge ok not-installed

Package: boost-endian
Version: 1.88.0
Depends: boost-cmake, boost-config, boost-headers
Architecture: x64-windows
Multi-Arch: same
Abi: 8ed6358afc24f1be32028e3ffebbbc73de7feb7bfe42464e86004aa8a639b4cd
Description: Boost endian module
Status: purge ok not-installed

Package: boost-spirit
Version: 1.88.0
Depends: boost-array, boost-assert, boost-cmake, boost-config, boost-core, boost-endian, boost-function, boost-function-types, boost-fusion, boost-headers, boost-integer, boost-io, boost-iterator, boost-move, boost-mpl, boost-optional, boost-phoenix, boost-pool, boost-preprocessor, boost-proto, boost-range, boost-regex, boost-smart-ptr, boost-static-assert, boost-thread, boost-throw-exception, boost-type-traits, boost-typeof, boost-unordered, boost-utility, boost-variant
Architecture: x64-windows
Multi-Arch: same
Abi: 5d83ad2a803cfcabbb012acd293bbd08468883b8be6efa7dae1109d78d98043e
Description: Boost spirit module
Status: purge ok not-installed

Package: boost-xpressive
Version: 1.88.0
Depends: boost-assert, boost-cmake, boost-config, boost-conversion, boost-core, boost-exception, boost-fusion, boost-headers, boost-integer, boost-iterator, boost-lexical-cast, boost-mpl, boost-numeric-conversion, boost-optional, boost-preprocessor, boost-proto, boost-range, boost-smart-ptr, boost-spirit, boost-static-assert, boost-throw-exception, boost-type-traits, boost-typeof, boost-utility
Architecture: x64-windows
Multi-Arch: same
Abi: bb54288e77e61175de37c84af83d86ba92efcff34d4d8d71c0ef9c61d01007bb
Description: Boost xpressive module
Status: purge ok not-installed

Package: boost-serialization
Version: 1.88.0
Depends: boost-array, boost-assert, boost-cmake, boost-config, boost-core, boost-detail, boost-function, boost-headers, boost-integer, boost-io, boost-iterator, boost-move, boost-mp11, boost-mpl, boost-optional, boost-predef, boost-preprocessor, boost-smart-ptr, boost-spirit, boost-static-assert, boost-throw-exception, boost-type-traits, boost-utility, boost-variant, boost-variant2
Architecture: x64-windows
Multi-Arch: same
Abi: e9a81128854a9a0852a996a34c71304417df91afcec1801b7a9927052b72e272
Description: Boost serialization module
Status: purge ok not-installed

Package: boost-multi-index
Version: 1.88.0
Depends: boost-assert, boost-bind, boost-cmake, boost-config, boost-container-hash, boost-core, boost-headers, boost-integer, boost-iterator, boost-move, boost-mpl, boost-preprocessor, boost-smart-ptr, boost-static-assert, boost-throw-exception, boost-tuple, boost-type-traits, boost-utility
Architecture: x64-windows
Multi-Arch: same
Abi: 91f5c9d80f81c84a068fce39ac11d9fa7f0be29d5f4f2025db4471035ca56a66
Description: Boost multi_index module
Status: purge ok not-installed

Package: boost-format
Version: 1.88.0
Depends: boost-assert, boost-cmake, boost-config, boost-core, boost-headers, boost-optional, boost-smart-ptr, boost-throw-exception, boost-utility
Architecture: x64-windows
Multi-Arch: same
Abi: a1d280edf4da0d340dfde3fe50d7b7dc57d654cb0be17a9e3d919e4a97ef85c0
Description: Boost format module
Status: purge ok not-installed

Package: boost-scope
Version: 1.88.0
Depends: boost-assert, boost-cmake, boost-config, boost-core, boost-headers, boost-type-traits
Architecture: x64-windows
Multi-Arch: same
Abi: 8a4d1fa5b398655e701089da3a072825d1225ce4a0eff5d22de8e25e7bb9ee03
Description: Boost scope module
Status: install ok installed

Package: boost-filesystem
Version: 1.88.0
Depends: boost-assert, boost-atomic, boost-cmake, boost-config, boost-container-hash, boost-core, boost-detail, boost-headers, boost-io, boost-iterator, boost-predef, boost-scope, boost-smart-ptr, boost-system, boost-type-traits, boost-winapi
Architecture: x64-windows
Multi-Arch: same
Abi: 80be2b733748b92c92624add96307d8e0f8b20e9481a5482b667e3ca15418a96
Description: Boost filesystem module
Status: install ok installed

Package: boost-wave
Version: 1.88.0
Depends: boost-assert, boost-cmake, boost-concept-check, boost-config, boost-core, boost-filesystem, boost-format, boost-headers, boost-iterator, boost-lexical-cast, boost-mpl, boost-multi-index, boost-optional, boost-pool, boost-preprocessor, boost-serialization, boost-smart-ptr, boost-spirit, boost-static-assert, boost-throw-exception, boost-type-traits
Architecture: x64-windows
Multi-Arch: same
Abi: e083ee6a62c010b018f85f3858f200d8859d20088c7541cf18d875f7de5e33a4
Description: Boost wave module
Status: purge ok not-installed

Package: boost-vmd
Version: 1.88.0
Depends: boost-cmake, boost-config, boost-headers, boost-preprocessor
Architecture: x64-windows
Multi-Arch: same
Abi: 2c92a1aa20fb1726f924d4e49ad03bfa4d9d5aecba748e39833ee507804b2156
Description: Boost vmd module
Status: purge ok not-installed

Package: boost-uuid
Version: 1.88.0
Depends: boost-assert, boost-cmake, boost-config, boost-headers, boost-throw-exception, boost-type-traits
Architecture: x64-windows
Multi-Arch: same
Abi: 8b1592b0ccd3ca863dead841d0ca2ac39a3958303f6a0bb13006f8da205b30e5
Description: Boost uuid module
Status: purge ok not-installed

Package: boost-url
Version: 1.88.0
Depends: boost-align, boost-assert, boost-cmake, boost-config, boost-core, boost-headers, boost-mp11, boost-optional, boost-static-assert, boost-system, boost-throw-exception, boost-type-traits, boost-variant2
Architecture: x64-windows
Multi-Arch: same
Abi: a99fb557d210d4aedafa553ca90bb8eb0f87f8bd5681ce28764712cc85b530c4
Description: Boost url module
Status: purge ok not-installed

Package: boost-dynamic-bitset
Version: 1.88.0
Depends: boost-assert, boost-cmake, boost-config, boost-container-hash, boost-core, boost-headers, boost-integer, boost-move, boost-static-assert, boost-throw-exception
Architecture: x64-windows
Multi-Arch: same
Abi: ca94cc494d3d31f729830e12e55e95efeae35e43d7a1e7e927f72ba4a4d8fb56
Description: Boost dynamic_bitset module
Status: purge ok not-installed

Package: boost-random
Version: 1.88.0
Depends: boost-assert, boost-cmake, boost-config, boost-core, boost-dynamic-bitset, boost-headers, boost-integer, boost-io, boost-static-assert, boost-system, boost-throw-exception, boost-type-traits, boost-utility
Architecture: x64-windows
Multi-Arch: same
Abi: 886fb4f12d62e1b2812042ea64fef7f432a7d63acbddf18fd9bdecfc8f3bd2a9
Description: Boost random module
Status: purge ok not-installed

Package: boost-math
Version: 1.88.0
Depends: boost-assert, boost-cmake, boost-concept-check, boost-config, boost-core, boost-headers, boost-integer, boost-lexical-cast, boost-predef, boost-random, boost-static-assert, boost-throw-exception
Architecture: x64-windows
Multi-Arch: same
Abi: 7c2a80dd5f0ac4a56d50eb65f825eabf54d59869cfde6f103e447b7bc5ccc0f9
Description: Boost math module
Status: purge ok not-installed

Package: boost-lambda
Version: 1.88.0
Depends: boost-bind, boost-cmake, boost-config, boost-core, boost-detail, boost-headers, boost-iterator, boost-mpl, boost-preprocessor, boost-tuple, boost-type-traits, boost-utility
Architecture: x64-windows
Multi-Arch: same
Abi: 8442f2ced1dc7a74630863945ebaf1af33e9b11424e4c251d3a231612b98d394
Description: Boost lambda module
Status: purge ok not-installed

Package: boost-units
Version: 1.88.0
Depends: boost-assert, boost-cmake, boost-config, boost-core, boost-headers, boost-integer, boost-io, boost-lambda, boost-math, boost-mpl, boost-preprocessor, boost-static-assert, boost-type-traits, boost-typeof
Architecture: x64-windows
Multi-Arch: same
Abi: 2022a5767ef61dcc13296a0b87d6d7118a4d21818d1d165123de42126f60503e
Description: Boost units module
Status: purge ok not-installed

Package: boost-logic
Version: 1.88.0
Depends: boost-cmake, boost-config, boost-core, boost-headers
Architecture: x64-windows
Multi-Arch: same
Abi: a4630119bab930ab649247a6e0bd46c13eca178289d2d0425c66f7c03341ed61
Description: Boost logic module
Status: purge ok not-installed

Package: boost-interval
Version: 1.88.0
Depends: boost-cmake, boost-config, boost-detail, boost-headers, boost-logic
Architecture: x64-windows
Multi-Arch: same
Abi: 506669e1859ac6bdf62104c2f2877e94fa9bfa9bc5b9fa2d0cf52748cbc74184
Description: Boost interval module
Status: purge ok not-installed

Package: boost-any
Version: 1.88.0
Depends: boost-assert, boost-cmake, boost-config, boost-headers, boost-throw-exception, boost-type-index
Architecture: x64-windows
Multi-Arch: same
Abi: ac2071edb17bc0e2c72ebe2bbab1882ac09c110d91580cceefd5adeaff380913
Description: Boost any module
Status: purge ok not-installed

Package: boost-property-tree
Version: 1.88.0
Depends: boost-any, boost-assert, boost-bind, boost-cmake, boost-config, boost-core, boost-headers, boost-iterator, boost-mpl, boost-multi-index, boost-optional, boost-range, boost-serialization, boost-static-assert, boost-throw-exception, boost-type-traits
Architecture: x64-windows
Multi-Arch: same
Abi: 847163eae3585b3112008e1a797a7f6e5b06b8bd2f705bf18d5c260f6054b922
Description: Boost property_tree module
Status: purge ok not-installed

Package: boost-compute
Version: 1.88.0
Depends: boost-algorithm, boost-array, boost-assert, boost-atomic, boost-chrono, boost-cmake, boost-config, boost-core, boost-filesystem, boost-function, boost-function-types, boost-fusion, boost-headers, boost-iterator, boost-lexical-cast, boost-mpl, boost-optional, boost-preprocessor, boost-property-tree, boost-proto, boost-range, boost-smart-ptr, boost-static-assert, boost-thread, boost-throw-exception, boost-tuple, boost-type-traits, boost-typeof, boost-utility, boost-uuid
Architecture: x64-windows
Multi-Arch: same
Abi: 498b7fd976b250640d0ac459fe2ddcac1a57f772c059d5b2d2b86437916c26e3
Description: Boost compute module
Status: purge ok not-installed

Package: boost-ublas
Version: 1.88.0
Depends: boost-cmake, boost-compute, boost-concept-check, boost-config, boost-core, boost-headers, boost-interval, boost-iterator, boost-mpl, boost-range, boost-serialization, boost-smart-ptr, boost-static-assert, boost-type-traits, boost-typeof
Architecture: x64-windows
Multi-Arch: same
Abi: 235622c93047eb351ca4cb9cc8c95d464b184d7f591edcaad81042d5d488c786
Description: Boost ublas module
Status: purge ok not-installed

Package: boost-type-erasure
Version: 1.88.0
Depends: boost-assert, boost-cmake, boost-config, boost-core, boost-fusion, boost-headers, boost-iterator, boost-mp11, boost-mpl, boost-preprocessor, boost-smart-ptr, boost-thread, boost-throw-exception, boost-type-traits, boost-typeof, boost-vmd
Architecture: x64-windows
Multi-Arch: same
Abi: 6950b4bbba08da8886378af4bd9730e0bbe75d2994e0a065368db16c2dda2ec7
Description: Boost type_erasure module
Status: purge ok not-installed

Package: boost-tti
Version: 1.88.0
Depends: boost-cmake, boost-config, boost-function-types, boost-headers, boost-mpl, boost-preprocessor, boost-type-traits
Architecture: x64-windows
Multi-Arch: same
Abi: 13990e15e97c06d1c5fa4c5f5a5503b68202579f17d7250f33af04972e23f4d1
Description: Boost tti module
Status: purge ok not-installed

Package: boost-timer
Version: 1.88.0
Depends: boost-cmake, boost-config, boost-headers, boost-io, boost-predef
Architecture: x64-windows
Multi-Arch: same
Abi: cab5fb42810a41ee47e3e98c91152f7fe0b0408cd975a7a6d6f5a4fe2f5103bc
Description: Boost timer module
Status: purge ok not-installed

Package: boost-test
Version: 1.88.0
Depends: boost-algorithm, boost-assert, boost-bind, boost-cmake, boost-config, boost-core, boost-detail, boost-exception, boost-function, boost-headers, boost-io, boost-iterator, boost-mpl, boost-numeric-conversion, boost-optional, boost-preprocessor, boost-smart-ptr, boost-static-assert, boost-type-traits, boost-utility
Architecture: x64-windows
Multi-Arch: same
Abi: 594dc31f1db3c9ace5a02197027a94028185a9bf83f1b1db207479087e3cb1bd
Description: Boost test module
Status: purge ok not-installed

Package: boost-stl-interfaces
Version: 1.88.0
Depends: boost-assert, boost-cmake, boost-config, boost-headers, boost-type-traits
Architecture: x64-windows
Multi-Arch: same
Abi: 5c88620c36dbaeeeec0b854e87234ca024109b21c327b573e690fb918da44dda
Description: Boost stl_interfaces module
Status: purge ok not-installed

Package: boost-static-string
Version: 1.88.0
Depends: boost-assert, boost-cmake, boost-config, boost-container-hash, boost-core, boost-headers, boost-static-assert, boost-throw-exception, boost-utility
Architecture: x64-windows
Multi-Arch: same
Abi: d4ac4e775ef55682553e585261b8844f8dfbfc65b7223f340181d51365d2edc8
Description: Boost static_string module
Status: purge ok not-installed

Package: boost-statechart
Version: 1.88.0
Depends: boost-assert, boost-bind, boost-cmake, boost-config, boost-conversion, boost-core, boost-detail, boost-function, boost-headers, boost-mpl, boost-smart-ptr, boost-static-assert, boost-thread, boost-type-traits
Architecture: x64-windows
Multi-Arch: same
Abi: 8893c6344c021e157a6d3c3cc612f1c65aecd4e4f9aa76cac9a58ea871c91301
Description: Boost statechart module
Status: purge ok not-installed

Package: boost-stacktrace
Version: 1.88.0
Depends: boost-assert, boost-cmake, boost-config, boost-container-hash, boost-core, boost-headers, boost-predef, boost-winapi
Architecture: x64-windows
Multi-Arch: same
Abi: 24f2f81e57e38f42812c2423cdc084a165498eb6e6b201dfd1805c6e45db4910
Description: Boost stacktrace module
Default-Features: windbg
Status: purge ok not-installed

Package: boost-stacktrace
Feature: windbg
Architecture: x64-windows
Multi-Arch: same
Description: Use boost_stacktrace_windbg
Status: purge ok not-installed

Package: boost-sort
Version: 1.88.0
Depends: boost-cmake, boost-config, boost-core, boost-headers, boost-range, boost-static-assert, boost-type-traits
Architecture: x64-windows
Multi-Arch: same
Abi: 199c429774e29aa7e538dcdd9ec57f5a7dbcdad8bdb839a87c4242e32543ee26
Description: Boost sort module
Status: purge ok not-installed

Package: gflags
Version: 2.2.2
Port-Version: 9
Depends: vcpkg-cmake, vcpkg-cmake-config
Architecture: x64-windows
Multi-Arch: same
Abi: 075875b30fc77c603f2a11af6633e789d586173ca15d4c92b545c10c4695e907
Description: A C++ library that implements commandline flags processing
Status: install ok installed

Package: jsoncpp
Version: 1.9.6
Depends: vcpkg-cmake, vcpkg-cmake-config
Architecture: x64-windows
Multi-Arch: same
Abi: 26109c6a069cb3694d5a9768f3df7b72dddac6314dc6e075007f8a227acab127
Description: JsonCpp is a C++ library that allows manipulating JSON values, including serialization and deserialization to and from strings. It can also preserve existing comment in unserialization/serialization steps, making it a convenient format to store user input files.
Status: install ok installed

